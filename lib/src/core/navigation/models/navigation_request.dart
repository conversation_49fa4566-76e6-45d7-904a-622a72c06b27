import 'package:equatable/equatable.dart';

/// Request for navigation operation
final class NavigationRequest extends Equatable {
  const NavigationRequest({
    required this.route,
    this.arguments,
    this.source = NavigationSource.manual,
    this.requiresAuth = false,
    this.requiresPremium = false,
    this.replaceAll = false,
    this.clearStack = false,
    this.metadata,
  });

  /// The route path to navigate to
  final String route;

  /// Arguments to pass to the route
  final Object? arguments;

  /// Source of the navigation request
  final NavigationSource source;

  /// Whether this route requires authentication
  final bool requiresAuth;

  /// Whether this route requires premium subscription
  final bool requiresPremium;

  /// Whether to replace all routes in the stack
  final bool replaceAll;

  /// Whether to clear the navigation stack
  final bool clearStack;

  /// Additional metadata for analytics/logging
  final Map<String, dynamic>? metadata;

  /// Create a copy with updated properties
  NavigationRequest copyWith({
    String? route,
    Object? arguments,
    NavigationSource? source,
    bool? requiresAuth,
    bool? requiresPremium,
    bool? replaceAll,
    bool? clearStack,
    Map<String, dynamic>? metadata,
  }) {
    return NavigationRequest(
      route: route ?? this.route,
      arguments: arguments ?? this.arguments,
      source: source ?? this.source,
      requiresAuth: requiresAuth ?? this.requiresAuth,
      requiresPremium: requiresPremium ?? this.requiresPremium,
      replaceAll: replaceAll ?? this.replaceAll,
      clearStack: clearStack ?? this.clearStack,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        route,
        arguments,
        source,
        requiresAuth,
        requiresPremium,
        replaceAll,
        clearStack,
        metadata,
      ];
}

/// Source of navigation request for analytics and logging
enum NavigationSource {
  manual,
  pushNotification,
  deepLink,
  urlScheme,
  universalLink,
  inAppNavigation,
  flowBuilder,
}
