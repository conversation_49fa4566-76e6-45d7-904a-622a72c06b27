import 'package:equatable/equatable.dart';

/// Result of a navigation operation
sealed class NavigationResult extends Equatable {
  const NavigationResult();
}

/// Successful navigation
final class NavigationSuccess extends NavigationResult {
  const NavigationSuccess({
    this.message,
    this.data,
  });

  final String? message;
  final Map<String, dynamic>? data;

  @override
  List<Object?> get props => [message, data];
}

/// Navigation failed with error
final class NavigationFailure extends NavigationResult {
  const NavigationFailure({
    required this.error,
    this.fallbackRoute,
    this.shouldShowError = true,
  });

  final String error;
  final String? fallbackRoute;
  final bool shouldShowError;

  @override
  List<Object?> get props => [error, fallbackRoute, shouldShowError];
}

/// Navigation was cancelled or blocked
final class NavigationCancelled extends NavigationResult {
  const NavigationCancelled({
    this.reason,
  });

  final String? reason;

  @override
  List<Object?> get props => [reason];
}

/// Navigation requires additional action (e.g., paywall)
final class NavigationRequiresAction extends NavigationResult {
  const NavigationRequiresAction({
    required this.action,
    required this.originalRoute,
    this.actionData,
  });

  final NavigationAction action;
  final String originalRoute;
  final Map<String, dynamic>? actionData;

  @override
  List<Object?> get props => [action, originalRoute, actionData];
}

/// Types of actions that may be required before navigation
enum NavigationAction {
  showPaywall,
  requireAuth,
  requirePermission,
  showDialog,
}
