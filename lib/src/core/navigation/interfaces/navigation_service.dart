import '../models/navigation_request.dart';
import '../models/navigation_result.dart';

/// Interface for navigation service
abstract interface class INavigationService {
  /// Navigate to a route with the given request
  Future<NavigationResult> navigate(NavigationRequest request);

  /// Navigate to a route by string path
  Future<NavigationResult> navigateToRoute(
    String route, {
    Object? arguments,
    NavigationSource source = NavigationSource.manual,
    bool requiresAuth = false,
    bool requiresPremium = false,
    bool replaceAll = false,
    bool clearStack = false,
    Map<String, dynamic>? metadata,
  });

  /// Navigate from push notification data
  Future<NavigationResult> navigateFromPushNotification(
    Map<String, dynamic> notificationData,
  );

  /// Navigate from deep link URL
  Future<NavigationResult> navigateFromDeepLink(
    String url, {
    Map<String, dynamic>? metadata,
  });

  /// Go back in navigation stack
  Future<NavigationResult> goBack();

  /// Clear navigation stack and go to route
  Future<NavigationResult> clearAndNavigateTo(String route);

  /// Check if a route can be navigated to
  Future<bool> canNavigateTo(String route);

  /// Get current route information
  String? getCurrentRoute();

  /// Register a navigation interceptor
  void addInterceptor(NavigationInterceptor interceptor);

  /// Remove a navigation interceptor
  void removeInterceptor(NavigationInterceptor interceptor);
}

/// Interface for navigation interceptors
abstract interface class NavigationInterceptor {
  /// Called before navigation occurs
  /// Return null to allow navigation, or NavigationResult to block/modify it
  Future<NavigationResult?> onBeforeNavigate(NavigationRequest request);

  /// Called after successful navigation
  Future<void> onAfterNavigate(NavigationRequest request, NavigationResult result);

  /// Called when navigation fails
  Future<void> onNavigationError(NavigationRequest request, NavigationResult error);
}
