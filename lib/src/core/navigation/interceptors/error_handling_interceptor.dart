import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../shared/shared.dart';
import '../interfaces/navigation_service.dart';
import '../models/navigation_request.dart';
import '../models/navigation_result.dart';

/// Navigation interceptor for error handling and user feedback
@injectable
class ErrorHandlingNavigationInterceptor implements NavigationInterceptor {
  @override
  Future<NavigationResult?> onBeforeNavigate(NavigationRequest request) async {
    // Validate request before navigation
    if (request.route.isEmpty) {
      return const NavigationFailure(
        error: 'Empty route provided',
        shouldShowError: false,
      );
    }

    // Add any pre-navigation validations here
    return null;
  }

  @override
  Future<void> onAfterNavigate(
    NavigationRequest request,
    NavigationResult result,
  ) async {
    // Handle successful navigation feedback if needed
    if (result is NavigationSuccess && result.message != null) {
      debugPrint('Navigation success: ${result.message}');
    }
  }

  @override
  Future<void> onNavigationError(
    NavigationRequest request,
    NavigationResult error,
  ) async {
    await _handleNavigationError(request, error);
  }

  /// Handle different types of navigation errors
  Future<void> _handleNavigationError(
    NavigationRequest request,
    NavigationResult error,
  ) async {
    switch (error) {
      case NavigationFailure failure:
        await _handleNavigationFailure(request, failure);
        break;

      case NavigationRequiresAction requiresAction:
        await _handleRequiredAction(request, requiresAction);
        break;

      case NavigationCancelled cancelled:
        await _handleNavigationCancelled(request, cancelled);
        break;

      default:
        debugPrint('Unknown navigation error type: ${error.runtimeType}');
    }
  }

  /// Handle navigation failures
  Future<void> _handleNavigationFailure(
    NavigationRequest request,
    NavigationFailure failure,
  ) async {
    debugPrint('Navigation failed: ${failure.error} for route: ${request.route}');

    // Show error to user if requested
    if (failure.shouldShowError) {
      SdmToast.show(_getUserFriendlyErrorMessage(failure.error));
    }

    // Navigate to fallback route if provided
    if (failure.fallbackRoute != null) {
      debugPrint('Navigating to fallback route: ${failure.fallbackRoute}');
      // Note: This would need access to the navigation service to avoid circular dependency
      // For now, just log the fallback route
    }
  }

  /// Handle required actions (like paywall, auth)
  Future<void> _handleRequiredAction(
    NavigationRequest request,
    NavigationRequiresAction requiresAction,
  ) async {
    debugPrint(
      'Navigation requires action: ${requiresAction.action.name} for route: ${request.route}',
    );

    switch (requiresAction.action) {
      case NavigationAction.showPaywall:
        await _handlePaywallRequired(request, requiresAction);
        break;

      case NavigationAction.requireAuth:
        await _handleAuthRequired(request, requiresAction);
        break;

      case NavigationAction.requirePermission:
        await _handlePermissionRequired(request, requiresAction);
        break;

      case NavigationAction.showDialog:
        await _handleDialogRequired(request, requiresAction);
        break;
    }
  }

  /// Handle navigation cancellation
  Future<void> _handleNavigationCancelled(
    NavigationRequest request,
    NavigationCancelled cancelled,
  ) async {
    debugPrint(
      'Navigation cancelled for route: ${request.route}. Reason: ${cancelled.reason}',
    );

    // Log cancellation for analytics
    // Could track user behavior patterns here
  }

  /// Handle paywall requirement
  Future<void> _handlePaywallRequired(
    NavigationRequest request,
    NavigationRequiresAction requiresAction,
  ) async {
    // This would integrate with your existing PaywallManager
    // For now, show a toast message
    SdmToast.show('Premium subscription required to access this feature');
    
    debugPrint('Paywall required for route: ${request.route}');
    debugPrint('Action data: ${requiresAction.actionData}');
  }

  /// Handle authentication requirement
  Future<void> _handleAuthRequired(
    NavigationRequest request,
    NavigationRequiresAction requiresAction,
  ) async {
    SdmToast.show('Please sign in to access this feature');
    
    debugPrint('Authentication required for route: ${request.route}');
    // Could trigger navigation to auth flow here
  }

  /// Handle permission requirement
  Future<void> _handlePermissionRequired(
    NavigationRequest request,
    NavigationRequiresAction requiresAction,
  ) async {
    SdmToast.show('You do not have permission to access this feature');
    
    debugPrint('Permission required for route: ${request.route}');
  }

  /// Handle dialog requirement
  Future<void> _handleDialogRequired(
    NavigationRequest request,
    NavigationRequiresAction requiresAction,
  ) async {
    debugPrint('Dialog required for route: ${request.route}');
    // Could show custom dialogs based on action data
  }

  /// Convert technical error messages to user-friendly ones
  String _getUserFriendlyErrorMessage(String technicalError) {
    if (technicalError.toLowerCase().contains('network')) {
      return 'Please check your internet connection and try again';
    } else if (technicalError.toLowerCase().contains('timeout')) {
      return 'Request timed out. Please try again';
    } else if (technicalError.toLowerCase().contains('invalid route')) {
      return 'The requested page could not be found';
    } else if (technicalError.toLowerCase().contains('permission')) {
      return 'You do not have permission to access this feature';
    } else {
      return 'Something went wrong. Please try again';
    }
  }
}
