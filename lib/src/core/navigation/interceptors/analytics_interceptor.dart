import 'package:injectable/injectable.dart';

import '../../../feature/feature.dart';
import '../interfaces/navigation_service.dart';
import '../models/navigation_request.dart';
import '../models/navigation_result.dart';

/// Navigation interceptor for analytics tracking
@injectable
class AnalyticsNavigationInterceptor implements NavigationInterceptor {
  AnalyticsNavigationInterceptor({
    this.analyticsCubit,
  });

  final AnalyticsCubit? analyticsCubit;

  @override
  Future<NavigationResult?> onBeforeNavigate(NavigationRequest request) async {
    // Don't block navigation, just track
    return null;
  }

  @override
  Future<void> onAfterNavigate(
    NavigationRequest request,
    NavigationResult result,
  ) async {
    if (analyticsCubit == null) return;

    try {
      // Track successful navigation
      if (result is NavigationSuccess) {
        await _trackNavigationEvent(request, result);
      }
    } catch (e) {
      // Don't let analytics errors affect navigation
      debugPrint('Analytics tracking error: $e');
    }
  }

  @override
  Future<void> onNavigationError(
    NavigationRequest request,
    NavigationResult error,
  ) async {
    if (analyticsCubit == null) return;

    try {
      // Track navigation errors
      await _trackNavigationError(request, error);
    } catch (e) {
      debugPrint('Analytics error tracking error: $e');
    }
  }

  /// Track successful navigation event
  Future<void> _trackNavigationEvent(
    NavigationRequest request,
    NavigationSuccess result,
  ) async {
    final eventData = {
      'route': request.route,
      'source': request.source.name,
      'has_arguments': request.arguments != null,
      'requires_auth': request.requiresAuth,
      'requires_premium': request.requiresPremium,
      'timestamp': DateTime.now().toIso8601String(),
      ...?request.metadata,
    };

    // Create appropriate analytics event based on route
    final event = _createAnalyticsEvent(request.route, eventData);
    if (event != null) {
      await analyticsCubit!.onTrackAnalyticsEvent(event);
    }
  }

  /// Track navigation error
  Future<void> _trackNavigationError(
    NavigationRequest request,
    NavigationResult error,
  ) async {
    final errorData = {
      'route': request.route,
      'source': request.source.name,
      'error_type': error.runtimeType.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (error is NavigationFailure) {
      errorData['error_message'] = error.error;
      errorData['should_show_error'] = error.shouldShowError;
    } else if (error is NavigationRequiresAction) {
      errorData['required_action'] = error.action.name;
      errorData['original_route'] = error.originalRoute;
    }

    // Track navigation error event
    await analyticsCubit!.onTrackAnalyticsEvent(
      NavigationErrorEvent(errorData),
    );
  }

  /// Create appropriate analytics event based on route
  AnalyticsEvent? _createAnalyticsEvent(
    String route,
    Map<String, dynamic> data,
  ) {
    switch (route) {
      case '/music-list':
      case '/music-player':
        return MusicNavigationEvent(data);

      case '/blogs':
      case '/blog-view':
        return BlogNavigationEvent(data);

      case '/videos':
      case '/video-player':
        return VideoNavigationEvent(data);

      case '/profile':
        return ProfileNavigationEvent(data);

      case '/certificates':
      case '/certificate-details':
        return CertificateNavigationEvent(data);

      default:
        return GeneralNavigationEvent(data);
    }
  }
}

/// Base analytics event for navigation
abstract class NavigationAnalyticsEvent extends AnalyticsEvent {
  NavigationAnalyticsEvent(this.data);

  final Map<String, dynamic> data;

  @override
  Map<String, dynamic> get parameters => data;
}

/// General navigation event
class GeneralNavigationEvent extends NavigationAnalyticsEvent {
  GeneralNavigationEvent(super.data);

  @override
  String get name => 'navigation_general';
}

/// Music-related navigation event
class MusicNavigationEvent extends NavigationAnalyticsEvent {
  MusicNavigationEvent(super.data);

  @override
  String get name => 'navigation_music';
}

/// Blog-related navigation event
class BlogNavigationEvent extends NavigationAnalyticsEvent {
  BlogNavigationEvent(super.data);

  @override
  String get name => 'navigation_blog';
}

/// Video-related navigation event
class VideoNavigationEvent extends NavigationAnalyticsEvent {
  VideoNavigationEvent(super.data);

  @override
  String get name => 'navigation_video';
}

/// Profile navigation event
class ProfileNavigationEvent extends NavigationAnalyticsEvent {
  ProfileNavigationEvent(super.data);

  @override
  String get name => 'navigation_profile';
}

/// Certificate navigation event
class CertificateNavigationEvent extends NavigationAnalyticsEvent {
  CertificateNavigationEvent(super.data);

  @override
  String get name => 'navigation_certificate';
}

/// Navigation error event
class NavigationErrorEvent extends NavigationAnalyticsEvent {
  NavigationErrorEvent(super.data);

  @override
  String get name => 'navigation_error';
}
