import 'package:injectable/injectable.dart';

import '../../../feature/feature.dart';
import '../models/navigation_request.dart';
import '../models/navigation_result.dart';
import 'route_mapper.dart';

/// Validates permissions for navigation requests
@injectable
class PermissionValidator {
  PermissionValidator({
    required this.routeMapper,
    this.subscriptionService,
  });

  final RouteMapper routeMapper;
  final SubscriptionService? subscriptionService;

  /// Validate if user can navigate to the requested route
  Future<NavigationResult?> validatePermissions(
    NavigationRequest request,
    UserSessionState? userSession,
  ) async {
    final requirements = routeMapper.getRouteRequirements(request.route);

    // Check authentication requirement
    if (requirements.requiresAuth && !_isAuthenticated(userSession)) {
      return NavigationRequiresAction(
        action: NavigationAction.requireAuth,
        originalRoute: request.route,
        actionData: {'redirectAfterAuth': request.route},
      );
    }

    // Check admin requirement
    if (requirements.requiresAdmin && !_isAdmin(userSession)) {
      return const NavigationFailure(
        error: 'Admin access required',
        shouldShowError: true,
      );
    }

    // Check premium requirement
    if (requirements.requiresPremium && !await _hasPremiumAccess(userSession)) {
      return NavigationRequiresAction(
        action: NavigationAction.showPaywall,
        originalRoute: request.route,
        actionData: {
          'feature': _getFeatureFromRoute(request.route),
          'source': request.source.name,
        },
      );
    }

    // All validations passed
    return null;
  }

  /// Check if user is authenticated
  bool _isAuthenticated(UserSessionState? userSession) {
    return userSession?.isAuthenticated ?? false;
  }

  /// Check if user is admin
  bool _isAdmin(UserSessionState? userSession) {
    return userSession?.isAdmin ?? false;
  }

  /// Check if user has premium access
  Future<bool> _hasPremiumAccess(UserSessionState? userSession) async {
    // In development, allow access
    if (FlavorConfig.isDevelopment()) {
      return true;
    }

    // Check admin access
    if (_isAdmin(userSession)) {
      return true;
    }

    // Check subscription service
    if (subscriptionService != null) {
      return subscriptionService!.hasMusicPremium;
    }

    // Fallback: no premium access
    return false;
  }

  /// Get feature name from route for analytics
  String _getFeatureFromRoute(String route) {
    if (route.startsWith('/music')) {
      return 'music';
    } else if (route.startsWith('/certificate')) {
      return 'certificates';
    } else if (route.startsWith('/admin')) {
      return 'admin';
    }
    return 'unknown';
  }

  /// Check if route requires specific permissions
  bool routeRequiresPermissions(String route) {
    final requirements = routeMapper.getRouteRequirements(route);
    return requirements.requiresAuth || 
           requirements.requiresPremium || 
           requirements.requiresAdmin;
  }
}
