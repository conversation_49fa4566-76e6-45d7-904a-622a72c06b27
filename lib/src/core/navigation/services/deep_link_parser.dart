import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

import '../models/navigation_request.dart';

/// Parses deep links and push notification data into navigation requests
@injectable
class DeepLinkParser {
  /// Parse push notification data into navigation request
  NavigationRequest? parseNotificationData(Map<String, dynamic> data) {
    if (data.isEmpty) return null;

    final route = data['routeName'] as String?;
    final argumentsData = data['arguments'];

    if (route == null) return null;

    return NavigationRequest(
      route: route,
      arguments: argumentsData,
      source: NavigationSource.pushNotification,
      metadata: {
        'notificationData': data,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Parse URL deep link into navigation request
  NavigationRequest? parseDeepLinkUrl(String url) {
    try {
      final uri = Uri.parse(url);

      // Handle different URL schemes
      switch (uri.scheme.toLowerCase()) {
        case 'shridattmandir':
          return _parseCustomScheme(uri);
        case 'https':
        case 'http':
          return _parseUniversalLink(uri);
        default:
          return null;
      }
    } catch (e) {
      // Invalid URL format
      debugPrint('Failed to parse deep link URL: $url, Error: $e');
      return null;
    }
  }

  /// Parse universal links (https/http)
  NavigationRequest? _parseUniversalLink(Uri uri) {
    // Handle universal links for your domain
    if (uri.host == 'shridattmandir.com' ||
        uri.host == 'www.shridattmandir.com') {
      return _parseCustomScheme(uri);
    }

    // Handle other universal link patterns
    return null;
  }

  /// Parse custom URL scheme
  NavigationRequest? _parseCustomScheme(Uri uri) {
    final path = uri.path.isEmpty ? '/' : uri.path;
    final queryParams = uri.queryParameters;

    // Map common deep link patterns
    switch (path) {
      case '/music':
        final musicId = queryParams['id'];
        if (musicId != null) {
          return NavigationRequest(
            route: '/music-player',
            arguments: musicId,
            source: NavigationSource.deepLink,
            requiresPremium: true,
            metadata: {
              'deepLinkUrl': uri.toString(),
              'musicId': musicId,
            },
          );
        }
        return const NavigationRequest(
          route: '/music-list',
          source: NavigationSource.deepLink,
          requiresPremium: true,
        );

      case '/blog':
        final blogId = queryParams['id'];
        if (blogId != null) {
          return NavigationRequest(
            route: '/blog-view',
            arguments: blogId,
            source: NavigationSource.deepLink,
            metadata: {
              'deepLinkUrl': uri.toString(),
              'blogId': blogId,
            },
          );
        }
        return const NavigationRequest(
          route: '/blogs',
          source: NavigationSource.deepLink,
        );

      case '/profile':
        return const NavigationRequest(
          route: '/profile',
          source: NavigationSource.deepLink,
          requiresAuth: true,
        );

      case '/certificates':
        return const NavigationRequest(
          route: '/certificates',
          source: NavigationSource.deepLink,
          requiresAuth: true,
        );

      default:
        // Try to map path directly to route
        if (_isValidRoutePath(path)) {
          return NavigationRequest(
            route: path,
            source: NavigationSource.deepLink,
            metadata: {
              'deepLinkUrl': uri.toString(),
              'queryParams': queryParams,
            },
          );
        }
        return null;
    }
  }

  /// Check if path is a valid route
  bool _isValidRoutePath(String path) {
    const validPaths = {
      '/home',
      '/about-us',
      '/blogs',
      '/videos',
      '/publications',
      '/contact-us',
      '/calendar-events',
    };
    return validPaths.contains(path);
  }

  /// Validate notification data structure
  bool isValidNotificationData(Map<String, dynamic> data) {
    return data.containsKey('routeName') &&
        data['routeName'] is String &&
        data['routeName'].toString().isNotEmpty;
  }

  /// Extract metadata from notification for analytics
  Map<String, dynamic> extractNotificationMetadata(Map<String, dynamic> data) {
    return {
      'messageId': data['messageId'],
      'sentTime': data['sentTime'],
      'category': data['category'],
      'priority': data['priority'],
      'source': 'push_notification',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
