import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/navigation_request.dart';

/// Manages navigation state for restoration after app restart
@injectable
class NavigationStateManager {
  static const String _navigationStateKey = 'navigation_state';
  static const String _pendingNavigationKey = 'pending_navigation';
  static const String _lastRouteKey = 'last_route';

  /// Save current navigation state
  Future<void> saveNavigationState(NavigationState state) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJson = jsonEncode(state.toJson());
      await prefs.setString(_navigationStateKey, stateJson);
      debugPrint('Navigation state saved: ${state.currentRoute}');
    } catch (e) {
      debugPrint('Failed to save navigation state: $e');
    }
  }

  /// Restore navigation state
  Future<NavigationState?> restoreNavigationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJson = prefs.getString(_navigationStateKey);
      
      if (stateJson != null) {
        final stateMap = jsonDecode(stateJson) as Map<String, dynamic>;
        final state = NavigationState.fromJson(stateMap);
        debugPrint('Navigation state restored: ${state.currentRoute}');
        return state;
      }
    } catch (e) {
      debugPrint('Failed to restore navigation state: $e');
    }
    return null;
  }

  /// Save pending navigation request (for delayed execution)
  Future<void> savePendingNavigation(NavigationRequest request) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final requestJson = jsonEncode(request.toJson());
      await prefs.setString(_pendingNavigationKey, requestJson);
      debugPrint('Pending navigation saved: ${request.route}');
    } catch (e) {
      debugPrint('Failed to save pending navigation: $e');
    }
  }

  /// Get and clear pending navigation request
  Future<NavigationRequest?> getPendingNavigation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final requestJson = prefs.getString(_pendingNavigationKey);
      
      if (requestJson != null) {
        // Clear the pending navigation
        await prefs.remove(_pendingNavigationKey);
        
        final requestMap = jsonDecode(requestJson) as Map<String, dynamic>;
        final request = NavigationRequest.fromJson(requestMap);
        debugPrint('Pending navigation retrieved: ${request.route}');
        return request;
      }
    } catch (e) {
      debugPrint('Failed to get pending navigation: $e');
    }
    return null;
  }

  /// Save last visited route
  Future<void> saveLastRoute(String route) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastRouteKey, route);
    } catch (e) {
      debugPrint('Failed to save last route: $e');
    }
  }

  /// Get last visited route
  Future<String?> getLastRoute() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastRouteKey);
    } catch (e) {
      debugPrint('Failed to get last route: $e');
      return null;
    }
  }

  /// Clear all navigation state
  Future<void> clearNavigationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await Future.wait([
        prefs.remove(_navigationStateKey),
        prefs.remove(_pendingNavigationKey),
        prefs.remove(_lastRouteKey),
      ]);
      debugPrint('Navigation state cleared');
    } catch (e) {
      debugPrint('Failed to clear navigation state: $e');
    }
  }

  /// Check if there's any saved navigation state
  Future<bool> hasNavigationState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_navigationStateKey) ||
             prefs.containsKey(_pendingNavigationKey);
    } catch (e) {
      return false;
    }
  }
}

/// Navigation state for restoration
class NavigationState {
  const NavigationState({
    required this.currentRoute,
    this.routeStack = const [],
    this.timestamp,
    this.metadata,
  });

  final String currentRoute;
  final List<String> routeStack;
  final DateTime? timestamp;
  final Map<String, dynamic>? metadata;

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'currentRoute': currentRoute,
      'routeStack': routeStack,
      'timestamp': timestamp?.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory NavigationState.fromJson(Map<String, dynamic> json) {
    return NavigationState(
      currentRoute: json['currentRoute'] as String,
      routeStack: (json['routeStack'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Check if state is recent (within last 24 hours)
  bool get isRecent {
    if (timestamp == null) return false;
    final now = DateTime.now();
    final difference = now.difference(timestamp!);
    return difference.inHours < 24;
  }

  /// Create a copy with updated properties
  NavigationState copyWith({
    String? currentRoute,
    List<String>? routeStack,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return NavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      routeStack: routeStack ?? this.routeStack,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Extension to add JSON serialization to NavigationRequest
extension NavigationRequestJson on NavigationRequest {
  Map<String, dynamic> toJson() {
    return {
      'route': route,
      'arguments': arguments?.toString(), // Simple string conversion
      'source': source.name,
      'requiresAuth': requiresAuth,
      'requiresPremium': requiresPremium,
      'replaceAll': replaceAll,
      'clearStack': clearStack,
      'metadata': metadata,
    };
  }

  static NavigationRequest fromJson(Map<String, dynamic> json) {
    return NavigationRequest(
      route: json['route'] as String,
      arguments: json['arguments'], // Keep as dynamic
      source: NavigationSource.values.firstWhere(
        (e) => e.name == json['source'],
        orElse: () => NavigationSource.manual,
      ),
      requiresAuth: json['requiresAuth'] as bool? ?? false,
      requiresPremium: json['requiresPremium'] as bool? ?? false,
      replaceAll: json['replaceAll'] as bool? ?? false,
      clearStack: json['clearStack'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
