import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

import '../../../feature/feature.dart';
import '../../../shared/shared.dart';
import '../interfaces/navigation_service.dart';
import '../models/navigation_request.dart';
import '../models/navigation_result.dart';
import 'deep_link_parser.dart';
import 'navigation_state_manager.dart';
import 'permission_validator.dart';
import 'route_mapper.dart';

/// Implementation of navigation service
@LazySingleton(as: INavigationService)
class NavigationServiceImpl implements INavigationService {
  NavigationServiceImpl({
    required this.routeMapper,
    required this.permissionValidator,
    required this.deepLinkParser,
    this.subscriptionService,
    this.navigationStateManager,
  });

  final RouteMapper routeMapper;
  final PermissionValidator permissionValidator;
  final DeepLinkParser deepLinkParser;
  final SubscriptionService? subscriptionService;
  final NavigationStateManager? navigationStateManager;

  final List<NavigationInterceptor> _interceptors = [];
  StackRouter? _router;
  UserSessionState? _currentUserSession;

  /// Set the current router instance
  void setRouter(StackRouter router) {
    _router = router;
  }

  /// Update current user session for permission checks
  void updateUserSession(UserSessionState userSession) {
    _currentUserSession = userSession;
  }

  @override
  Future<NavigationResult> navigate(NavigationRequest request) async {
    try {
      // Run interceptors
      for (final interceptor in _interceptors) {
        final result = await interceptor.onBeforeNavigate(request);
        if (result != null) {
          await interceptor.onNavigationError(request, result);
          return result;
        }
      }

      // Validate route exists
      if (!routeMapper.isValidRoute(request.route)) {
        final result = NavigationFailure(
          error: 'Invalid route',
          shouldShowError: false,
        );
        await _notifyInterceptorsError(request, result);
        return result;
      }

      // Handle special routes
      if (request.route == '/paywall') {
        return await _handlePaywallRoute(request);
      }

      // Validate permissions
      final permissionResult = await permissionValidator.validatePermissions(
        request,
        _currentUserSession,
      );
      if (permissionResult != null) {
        await _notifyInterceptorsError(request, permissionResult);
        return permissionResult;
      }

      // Map to auto_route
      final pageRoute =
          routeMapper.mapStringToRoute(request.route, request.arguments);
      if (pageRoute == null) {
        final result = NavigationFailure(
          error: 'Failed to map route',
          shouldShowError: false,
        );
        await _notifyInterceptorsError(request, result);
        return result;
      }

      // Perform navigation
      final result = await _performNavigation(request, pageRoute);
      await _notifyInterceptorsSuccess(request, result);
      return result;
    } catch (e) {
      final result = NavigationFailure(
        error: 'Navigation failed: $e',
        shouldShowError: true,
      );
      await _notifyInterceptorsError(request, result);
      return result;
    }
  }

  @override
  Future<NavigationResult> navigateToRoute(
    String route, {
    Object? arguments,
    NavigationSource source = NavigationSource.manual,
    bool requiresAuth = false,
    bool requiresPremium = false,
    bool replaceAll = false,
    bool clearStack = false,
    Map<String, dynamic>? metadata,
  }) async {
    final request = NavigationRequest(
      route: route,
      arguments: arguments,
      source: source,
      requiresAuth: requiresAuth,
      requiresPremium: requiresPremium,
      replaceAll: replaceAll,
      clearStack: clearStack,
      metadata: metadata,
    );

    return navigate(request);
  }

  @override
  Future<NavigationResult> navigateFromPushNotification(
    Map<String, dynamic> notificationData,
  ) async {
    final request = deepLinkParser.parseNotificationData(notificationData);
    if (request == null) {
      return const NavigationFailure(
        error: 'Invalid notification data',
        shouldShowError: false,
      );
    }

    return navigate(request);
  }

  @override
  Future<NavigationResult> navigateFromDeepLink(
    String url, {
    Map<String, dynamic>? metadata,
  }) async {
    final request = deepLinkParser.parseDeepLinkUrl(url);
    if (request == null) {
      return const NavigationFailure(
        error: 'Invalid deep link URL',
        shouldShowError: false,
      );
    }

    // Add additional metadata
    final updatedRequest = request.copyWith(
      metadata: {...?request.metadata, ...?metadata},
    );

    return navigate(updatedRequest);
  }

  @override
  Future<NavigationResult> goBack() async {
    try {
      if (_router?.canPop() == true) {
        _router!.pop();
        return const NavigationSuccess(message: 'Navigated back');
      } else {
        return const NavigationFailure(
          error: 'Cannot go back',
          shouldShowError: false,
        );
      }
    } catch (e) {
      return NavigationFailure(
        error: 'Failed to go back: $e',
        shouldShowError: true,
      );
    }
  }

  @override
  Future<NavigationResult> clearAndNavigateTo(String route) async {
    final request = NavigationRequest(
      route: route,
      source: NavigationSource.manual,
      replaceAll: true,
    );

    return navigate(request);
  }

  @override
  Future<bool> canNavigateTo(String route) async {
    if (!routeMapper.isValidRoute(route)) {
      return false;
    }

    final request = NavigationRequest(route: route);
    final permissionResult = await permissionValidator.validatePermissions(
      request,
      _currentUserSession,
    );

    return permissionResult == null;
  }

  @override
  String? getCurrentRoute() {
    return _router?.current.name;
  }

  @override
  void addInterceptor(NavigationInterceptor interceptor) {
    _interceptors.add(interceptor);
  }

  @override
  void removeInterceptor(NavigationInterceptor interceptor) {
    _interceptors.remove(interceptor);
  }

  /// Handle paywall route specially
  Future<NavigationResult> _handlePaywallRoute(
      NavigationRequest request) async {
    // This would integrate with your existing PaywallManager
    // For now, return success as paywall is handled separately
    return const NavigationSuccess(
      message: 'Paywall shown',
      data: {'action': 'paywall_displayed'},
    );
  }

  /// Perform the actual navigation
  Future<NavigationResult> _performNavigation(
    NavigationRequest request,
    PageRouteInfo pageRoute,
  ) async {
    if (_router == null) {
      return const NavigationFailure(
        error: 'Router not initialized',
        shouldShowError: true,
      );
    }

    if (request.replaceAll || request.clearStack) {
      await _router!.pushAndClearStack(pageRoute);
    } else {
      await _router!.push(pageRoute);
    }

    return NavigationSuccess(
      message: 'Navigated to ${request.route}',
      data: {
        'route': request.route,
        'source': request.source.name,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Notify interceptors of successful navigation
  Future<void> _notifyInterceptorsSuccess(
    NavigationRequest request,
    NavigationResult result,
  ) async {
    for (final interceptor in _interceptors) {
      try {
        await interceptor.onAfterNavigate(request, result);
      } catch (e) {
        debugPrint('Navigation interceptor error: $e');
      }
    }
  }

  /// Notify interceptors of navigation error
  Future<void> _notifyInterceptorsError(
    NavigationRequest request,
    NavigationResult result,
  ) async {
    for (final interceptor in _interceptors) {
      try {
        await interceptor.onNavigationError(request, result);
      } catch (e) {
        debugPrint('Navigation interceptor error: $e');
      }
    }
  }
}
