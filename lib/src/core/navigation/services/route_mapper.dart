import 'package:auto_route/auto_route.dart';
import 'package:injectable/injectable.dart';

import '../../router/app_router.dart';
import '../../../feature/feature.dart';

/// Maps string routes to auto_route PageRouteInfo
@injectable
class RouteMapper {
  /// Map a string route to PageRouteInfo
  PageRouteInfo? mapStringToRoute(String route, Object? arguments) {
    switch (route) {
      // Authentication Routes
      case '/phone-registration':
        return const PhoneRegistrationRoute();
      case '/phone-otp':
        if (arguments is String) {
          return PhoneOtpRoute(verificationId: arguments);
        }
        return PhoneOtpRoute();

      // Main App Routes
      case '/home':
        return const HomeRoute();
      case '/profile':
        return const ProfileRoute();

      // Content Routes
      case '/about-us':
        return const AboutUsRoute();
      case '/blogs':
        return const BlogsRoute();
      case '/blog-view':
        if (arguments is BlogModel) {
          return BlogViewRoute(blogModel: arguments);
        }
        return null; // Invalid arguments
      case '/calendar-events':
        return const CalendarEventsRoute();
      case '/contact-us':
        return const ContactUsRoute();
      case '/publications':
        return const PublicationsRoute();
      case '/publication-info':
        if (arguments is PublicationModel) {
          return PublicationInfoRoute(publicationModel: arguments);
        }
        return null; // Invalid arguments
      case '/videos':
        return const VideosRoute();
      case '/video-player':
        if (arguments is VideoModel) {
          return YoutubeVideoPlayerRoute(videoModel: arguments);
        }
        return null; // Invalid arguments

      // Music Routes
      case '/music-list':
        return const MusicListRoute();
      case '/music-player':
        if (arguments is MusicPlayerArguments) {
          return MusicPlayerRoute(musicPlayerArguments: arguments);
        }
        return MusicPlayerRoute();

      // Certificate Routes
      case '/enter-email':
        return const EnterEmailRoute();
      case '/certificates':
        if (arguments is AccredibleCredentialsCertificates) {
          return CertificatesRoute(certificates: arguments);
        }
        return CertificatesRoute();
      case '/certificate-details':
        if (arguments is Credential) {
          return CertificateDetailsRoute(credential: arguments);
        }
        return null; // Invalid arguments

      // Admin Routes
      case '/admin-home':
        return const AdminHomeRoute();
      case '/blog-input':
        return const BlogInputRoute();
      case '/publication-input':
        return const PublicationInputRoute();
      case '/quotes-input':
        return const QuotesInputRoute();
      case '/music-input':
        if (arguments is MusicModel) {
          return MusicInputRoute(musicModel: arguments);
        }
        return MusicInputRoute();

      // Web View Route
      case '/sdm-web-view':
        if (arguments is SdmWebViewArguments) {
          return SdmWebViewRoute(args: arguments);
        }
        return null; // Invalid arguments

      // Music Playlist Route
      case '/music-playlist':
        return const MusicPlaylistRoute();

      default:
        return null; // Unknown route
    }
  }

  /// Get route requirements (auth, premium, etc.)
  RouteRequirements getRouteRequirements(String route) {
    switch (route) {
      case '/music-list':
      case '/music-player':
      case '/music-playlist':
        return const RouteRequirements(
          requiresAuth: true,
          requiresPremium: true,
        );

      case '/admin-home':
      case '/blog-input':
      case '/publication-input':
      case '/quotes-input':
      case '/music-input':
        return const RouteRequirements(
          requiresAuth: true,
          requiresAdmin: true,
        );

      case '/profile':
      case '/certificates':
      case '/certificate-details':
      case '/enter-email':
        return const RouteRequirements(
          requiresAuth: true,
        );

      default:
        return const RouteRequirements();
    }
  }

  /// Check if a route is valid
  bool isValidRoute(String route) {
    return mapStringToRoute(route, null) != null || 
           _isSpecialRoute(route);
  }

  /// Check if route is a special route (like paywall)
  bool _isSpecialRoute(String route) {
    return route == '/paywall';
  }
}

/// Requirements for accessing a route
class RouteRequirements {
  const RouteRequirements({
    this.requiresAuth = false,
    this.requiresPremium = false,
    this.requiresAdmin = false,
  });

  final bool requiresAuth;
  final bool requiresPremium;
  final bool requiresAdmin;
}
