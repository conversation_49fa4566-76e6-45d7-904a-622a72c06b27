import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';

import '../interfaces/navigation_service.dart';
import '../models/navigation_request.dart';
import '../models/navigation_result.dart';

/// Handles deep links from various sources with proper app lifecycle management
@injectable
class DeepLinkHandler {
  DeepLinkHandler({
    required this.navigationService,
  });

  final INavigationService navigationService;

  static const MethodChannel _channel = MethodChannel('deep_links');
  StreamSubscription<String>? _linkSubscription;
  String? _pendingLink;
  bool _isAppReady = false;

  /// Initialize deep link handling
  Future<void> initialize() async {
    try {
      // Handle initial link (cold start)
      await _handleInitialLink();

      // Listen for incoming links (warm start)
      _linkSubscription = _linkStream.listen(
        _handleIncomingLink,
        onError: (error) {
          debugPrint('Deep link stream error: $error');
        },
      );
    } catch (e) {
      debugPrint('Failed to initialize deep link handler: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
    _linkSubscription = null;
  }

  /// Mark app as ready to handle deep links
  void markAppReady() {
    _isAppReady = true;
    
    // Process any pending link
    if (_pendingLink != null) {
      _handleIncomingLink(_pendingLink!);
      _pendingLink = null;
    }
  }

  /// Handle initial link when app starts (cold start)
  Future<void> _handleInitialLink() async {
    try {
      final initialLink = await _getInitialLink();
      if (initialLink != null) {
        debugPrint('Handling initial deep link: $initialLink');
        await _processDeepLink(initialLink, isInitialLink: true);
      }
    } catch (e) {
      debugPrint('Error handling initial link: $e');
    }
  }

  /// Handle incoming links while app is running (warm start)
  void _handleIncomingLink(String link) {
    debugPrint('Handling incoming deep link: $link');
    
    if (_isAppReady) {
      _processDeepLink(link, isInitialLink: false);
    } else {
      // Store link to process when app is ready
      _pendingLink = link;
      debugPrint('App not ready, storing pending link: $link');
    }
  }

  /// Process deep link and navigate
  Future<void> _processDeepLink(String link, {required bool isInitialLink}) async {
    try {
      final result = await navigationService.navigateFromDeepLink(
        link,
        metadata: {
          'isInitialLink': isInitialLink,
          'timestamp': DateTime.now().toIso8601String(),
          'source': isInitialLink ? 'cold_start' : 'warm_start',
        },
      );

      await _handleNavigationResult(result, link);
    } catch (e) {
      debugPrint('Error processing deep link: $e');
    }
  }

  /// Handle navigation result and provide user feedback
  Future<void> _handleNavigationResult(NavigationResult result, String link) async {
    switch (result) {
      case NavigationSuccess _:
        debugPrint('Deep link navigation successful: $link');
        break;

      case NavigationFailure failure:
        debugPrint('Deep link navigation failed: ${failure.error}');
        if (failure.shouldShowError) {
          // Could show a toast or dialog here
        }
        break;

      case NavigationRequiresAction requiresAction:
        debugPrint('Deep link requires action: ${requiresAction.action.name}');
        // The navigation service interceptors will handle this
        break;

      case NavigationCancelled cancelled:
        debugPrint('Deep link navigation cancelled: ${cancelled.reason}');
        break;
    }
  }

  /// Get initial link from platform
  Future<String?> _getInitialLink() async {
    try {
      return await _channel.invokeMethod<String>('getInitialLink');
    } on PlatformException catch (e) {
      debugPrint('Failed to get initial link: ${e.message}');
      return null;
    }
  }

  /// Stream of incoming links
  Stream<String> get _linkStream {
    return _channel.receiveBroadcastStream().cast<String>();
  }

  /// Check if a link is a valid deep link for this app
  bool isValidDeepLink(String link) {
    try {
      final uri = Uri.parse(link);
      
      // Check for supported schemes
      const supportedSchemes = ['shridattmandir', 'https', 'http'];
      if (!supportedSchemes.contains(uri.scheme.toLowerCase())) {
        return false;
      }

      // For https/http, check if it's our domain
      if (uri.scheme.toLowerCase() == 'https' || uri.scheme.toLowerCase() == 'http') {
        const supportedHosts = ['shridattmandir.com', 'www.shridattmandir.com'];
        return supportedHosts.contains(uri.host.toLowerCase());
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Manually process a deep link (for testing or special cases)
  Future<NavigationResult> processLink(String link) async {
    if (!isValidDeepLink(link)) {
      return const NavigationFailure(
        error: 'Invalid deep link format',
        shouldShowError: false,
      );
    }

    return await navigationService.navigateFromDeepLink(
      link,
      metadata: {
        'source': 'manual',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Get current pending link (for debugging)
  String? get pendingLink => _pendingLink;

  /// Check if app is ready to handle links
  bool get isAppReady => _isAppReady;
}
