import 'package:auto_route/auto_route.dart';
import 'package:flutter/foundation.dart';

import '../../feature/feature.dart';
import '../router/app_router.dart';

/// Simple navigation helper - reactive and clean
class NavigationHelper {
  static StackRouter? _router;

  /// Set router instance
  static void setRouter(StackRouter router) {
    _router = router;
  }

  /// Navigate from notification data - simple and reactive
  static Future<void> navigateFromNotification(
    Map<String, dynamic> data,
    SubscriptionService subscriptionService,
  ) async {
    if (_router == null) return;

    final route = data['routeName'] as String?;
    final arguments = data['arguments'];

    if (route == null) return;

    // Check permissions reactively
    if (await _requiresPaywall(route, subscriptionService)) {
      await PaywallManager.showPaywallModal(
        context: _router!.navigatorKey.currentContext!,
      );
      return;
    }

    final pageRoute = _mapToRoute(route, arguments);
    if (pageRoute != null) {
      await _router!.push(pageRoute);
    }
  }

  /// Map route string to PageRouteInfo - simple switch expression
  static PageRouteInfo? _mapToRoute(String route, dynamic arguments) {
    return switch (route) {
      '/home' => const HomeRoute(),
      '/profile' => const ProfileRoute(),
      '/about-us' => const AboutUsRoute(),
      '/blogs' => const BlogsRoute(),
      '/blog-view' when arguments is BlogModel => BlogViewRoute(blogModel: arguments),
      '/music-list' => const MusicListRoute(),
      '/music-player' when arguments is MusicPlayerArguments => 
        MusicPlayerRoute(musicPlayerArguments: arguments),
      '/music-player' => MusicPlayerRoute(),
      '/videos' => const VideosRoute(),
      '/video-player' when arguments is VideoModel => 
        YoutubeVideoPlayerRoute(videoModel: arguments),
      '/certificates' when arguments is AccredibleCredentialsCertificates => 
        CertificatesRoute(certificates: arguments),
      '/certificates' => CertificatesRoute(),
      '/admin-home' => const AdminHomeRoute(),
      '/publication-info' when arguments is PublicationModel =>
        PublicationInfoRoute(publicationModel: arguments),
      '/publications' => const PublicationsRoute(),
      '/contact-us' => const ContactUsRoute(),
      '/calendar-events' => const CalendarEventsRoute(),
      '/enter-email' => const EnterEmailRoute(),
      '/certificate-details' when arguments is Credential =>
        CertificateDetailsRoute(credential: arguments),
      '/blog-input' => const BlogInputRoute(),
      '/publication-input' => const PublicationInputRoute(),
      '/quotes-input' => const QuotesInputRoute(),
      '/music-input' when arguments is MusicModel =>
        MusicInputRoute(musicModel: arguments),
      '/music-input' => MusicInputRoute(),
      '/sdm-web-view' when arguments is SdmWebViewArguments =>
        SdmWebViewRoute(args: arguments),
      '/music-playlist' => const MusicPlaylistRoute(),
      _ => null,
    };
  }

  /// Check if route requires paywall - simple reactive check
  static Future<bool> _requiresPaywall(
    String route,
    SubscriptionService subscriptionService,
  ) async {
    if (!FlavorConfig.isProduction()) return false;

    return switch (route) {
      '/music-list' || '/music-player' => !subscriptionService.hasMusicPremium,
      _ => false,
    };
  }

  /// Navigate from deep link URL
  static Future<void> navigateFromDeepLink(String url) async {
    if (_router == null) return;

    final uri = Uri.tryParse(url);
    if (uri == null) return;

    final route = _parseDeepLink(uri);
    if (route != null) {
      await _router!.push(route);
    }
  }

  /// Parse deep link URL to route
  static PageRouteInfo? _parseDeepLink(Uri uri) {
    return switch (uri.path) {
      '/music' => const MusicListRoute(),
      '/blogs' => const BlogsRoute(),
      '/profile' => const ProfileRoute(),
      '/about' => const AboutUsRoute(),
      _ => const HomeRoute(),
    };
  }
}
