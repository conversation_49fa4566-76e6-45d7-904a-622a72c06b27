import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../feature/feature.dart';
import '../interfaces/navigation_service.dart';
import '../services/deep_link_parser.dart';
import '../services/navigation_service_impl.dart';
import '../services/permission_validator.dart';
import '../services/route_mapper.dart';
import '../interceptors/analytics_interceptor.dart';
import '../interceptors/error_handling_interceptor.dart';

/// Provider for navigation service with all dependencies
class NavigationProvider extends StatelessWidget {
  const NavigationProvider({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        // Core navigation services
        RepositoryProvider<RouteMapper>(
          create: (context) => RouteMapper(),
        ),
        RepositoryProvider<DeepLinkParser>(
          create: (context) => DeepLinkParser(),
        ),
        RepositoryProvider<PermissionValidator>(
          create: (context) => PermissionValidator(
            routeMapper: context.read<RouteMapper>(),
            subscriptionService: context.read<SubscriptionService>(),
          ),
        ),
        
        // Navigation service
        RepositoryProvider<INavigationService>(
          create: (context) {
            final service = NavigationServiceImpl(
              routeMapper: context.read<RouteMapper>(),
              permissionValidator: context.read<PermissionValidator>(),
              deepLinkParser: context.read<DeepLinkParser>(),
              subscriptionService: context.read<SubscriptionService>(),
            );

            // Add interceptors
            service.addInterceptor(
              AnalyticsNavigationInterceptor(
                analyticsCubit: context.read<AnalyticsCubit>(),
              ),
            );
            service.addInterceptor(ErrorHandlingNavigationInterceptor());

            return service;
          },
        ),
      ],
      child: child,
    );
  }
}
