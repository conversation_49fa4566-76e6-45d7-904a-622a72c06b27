import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/assets/fonts.gen.dart';

class SdmPalette {
  SdmPalette._();

  static const Color primary = Color(0xffF29BA1);

  static const Color white = Color(0xffffffff);
  static const Color white10 = Color(0xffF1F1F1);
  static const Color black = Color(0xff000000);
  static const Color black26 = Color(0x42000000);
  static const Color black29 = Color(0x29000000);
  static const Color black54 = Color(0x89000000);
  static const Color transparent = Color(0x00000000);
  static const Color blueaccent = Color(0xFF448AFF);
  static const Color blue = Color(0xFF2196F3);
  static const Color amber = Color(0xFFFFC107);
  static const Color textColorGrey = Color(0xff656464);
  static const Color textColorGrey2 = Color(0xffA5A1A1);
  static const Color lightRed = Color(0xffDF5E67);
  static const Color progressBarGrey = Color(0xff707070);

  static ThemeData sdmThemeData() {
    return ThemeData(
      useMaterial3: false,
      scaffoldBackgroundColor: SdmPalette.white10,
      buttonTheme: const ButtonThemeData(
        buttonColor: SdmPalette.primary,
      ),
      splashFactory: NoSplash.splashFactory,
      snackBarTheme: SnackBarThemeData(
        backgroundColor: SdmPalette.primary,
        contentTextStyle: TextStyle(
          color: SdmPalette.white,
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
          fontFamily: SdmFonts.montserrat,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 4,
        actionTextColor: SdmPalette.white,
        behavior: SnackBarBehavior.floating,
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: SdmPalette.lightRed,
      ),
      fontFamily: SdmFonts.montserrat,
      appBarTheme: AppBarTheme(
        color: SdmPalette.transparent,
        iconTheme: IconThemeData(
          color: SdmPalette.black,
          size: 20.h,
        ),
        titleTextStyle: TextStyle(
          color: SdmPalette.black,
          fontSize: 20.sp,
          fontWeight: FontWeight.bold,
          fontFamily: SdmFonts.montserrat,
        ),
        elevation: 0.0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: SdmPalette.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
    );
  }
}
