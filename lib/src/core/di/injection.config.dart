// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:device_info_plus/device_info_plus.dart' as _i833;
import 'package:dio/dio.dart' as _i361;
import 'package:firebase_analytics/firebase_analytics.dart' as _i398;
import 'package:firebase_auth/firebase_auth.dart' as _i59;
import 'package:firebase_messaging/firebase_messaging.dart' as _i892;
import 'package:firebase_storage/firebase_storage.dart' as _i457;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:just_audio/just_audio.dart' as _i501;

import '../../feature/auth/data/repositories/firebase_auth_repository.dart'
    as _i183;
import '../../feature/auth/domain/repositories/auth_repository.dart' as _i488;
import '../../feature/auth/domain/usecases/send_otp_usecase.dart' as _i772;
import '../../feature/auth/domain/usecases/sign_out_usecase.dart' as _i858;
import '../../feature/auth/domain/usecases/verify_otp_usecase.dart' as _i2;
import '../../feature/auth/presentation/bloc/auth_bloc.dart' as _i466;
import '../../feature/blogs/bloc/blogs_cubit.dart' as _i743;
import '../../feature/deeplink/cubit/deeplink_cubit.dart' as _i105;
import '../../feature/feature.dart' as _i688;
import '../../feature/music/cubits/music-list-cubit/music_list_cubit.dart'
    as _i213;
import '../../feature/music/repository/music_repository.dart' as _i605;
import 'service_locator.dart' as _i105;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i743.BlogsCubit>(() => _i743.BlogsCubit());
    gh.singleton<_i59.FirebaseAuth>(() => registerModule.firebaseAuth);
    gh.singleton<_i974.FirebaseFirestore>(
        () => registerModule.firebaseFirestore);
    gh.singleton<_i457.FirebaseStorage>(() => registerModule.firebaseStorage);
    gh.singleton<_i398.FirebaseAnalytics>(
        () => registerModule.firebaseAnalytics);
    gh.singleton<_i892.FirebaseMessaging>(
        () => registerModule.firebaseMessaging);
    gh.singleton<_i833.DeviceInfoPlugin>(() => registerModule.deviceInfoPlugin);
    gh.singleton<_i501.AudioPlayer>(() => registerModule.audioPlayer);
    gh.singleton<_i361.Dio>(() => registerModule.dio);
    gh.lazySingleton<_i488.AuthRepository>(
        () => _i183.FirebaseAuthRepository(gh<_i59.FirebaseAuth>()));
    gh.factory<_i605.MusicRepository>(() => _i605.MusicRepository(
          gh<_i974.FirebaseFirestore>(),
          gh<_i457.FirebaseStorage>(),
        ));
    gh.factory<_i772.SendOtpUseCase>(
        () => _i772.SendOtpUseCase(gh<_i488.AuthRepository>()));
    gh.factory<_i2.VerifyOtpUseCase>(
        () => _i2.VerifyOtpUseCase(gh<_i488.AuthRepository>()));
    gh.factory<_i858.SignOutUseCase>(
        () => _i858.SignOutUseCase(gh<_i488.AuthRepository>()));
    gh.factory<_i105.DeeplinkCubit>(() => _i105.DeeplinkCubit(
          subscriptionService: gh<_i688.SubscriptionService>(),
          firebaseMessaging: gh<_i892.FirebaseMessaging>(),
          musicRepository: gh<_i688.MusicRepository>(),
          blogsRepository: gh<_i688.BlogsRepository>(),
        ));
    gh.factory<_i466.AuthBloc>(() => _i466.AuthBloc(
          authRepository: gh<_i488.AuthRepository>(),
          sendOtpUseCase: gh<_i772.SendOtpUseCase>(),
          verifyOtpUseCase: gh<_i2.VerifyOtpUseCase>(),
          signOutUseCase: gh<_i858.SignOutUseCase>(),
        ));
    gh.factory<_i213.MusicListCubit>(() => _i213.MusicListCubit(
          musicRepository: gh<_i688.MusicRepository>(),
          profileRepository: gh<_i688.ProfileRepository>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i105.RegisterModule {}
