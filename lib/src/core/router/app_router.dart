import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/core.dart' show MusicPlayerArguments, SdmWebViewArguments;
import '../../feature/about-us/view/about_us_screen.dart';
import '../../feature/admin/view/admin_home_screen.dart';
import '../../feature/auth/presentation/screens/phone_otp_screen.dart';
import '../../feature/auth/presentation/screens/phone_registration_screen.dart';
import '../../feature/blogs/bloc/blogs_cubit.dart';
import '../../feature/blogs/models/blog_model.dart';
import '../../feature/blogs/view/blog_view.dart';
import '../../feature/blogs/view/blogs_input_screen.dart';
import '../../feature/blogs/view/blogs_screen.dart';
import '../../feature/calendar-events/view/calendar_events_screen.dart';
import '../../feature/certificate/models/accredible_credentials_certificates_model.dart';
import '../../feature/certificate/view/certificate_details.dart';
import '../../feature/certificate/view/certificates_screen.dart';
import '../../feature/certificate/view/enter_email_screen.dart';
import '../../feature/contact-us/view/contact_us_screen.dart';
import '../../feature/home/<USER>/home_cubit.dart';
import '../../feature/home/<USER>/quotes_input_screen.dart';
import '../../feature/home/<USER>/sdm_home.dart';
import '../../feature/music/cubits/music-list-cubit/music_list_cubit.dart';
import '../../feature/music/models/music_model.dart';
import '../../feature/music/repository/music_repository.dart';
import '../../feature/music/view/music-list/music_favorites_screen.dart';
import '../../feature/music/view/music-list/music_input_screen.dart';
import '../../feature/music/view/music-list/music_list.dart';
import '../../feature/music/view/music-player/music_player.dart';
import '../../feature/profile/bloc/profile_cubit.dart';
import '../../feature/profile/repository/profile_repository.dart';
import '../../feature/profile/view/profile_screen.dart';
import '../../feature/publications/bloc/publications_cubit.dart';
import '../../feature/publications/models/publication_model.dart';
import '../../feature/publications/view/publication_info.dart';
import '../../feature/publications/view/publications_input_screen.dart';
import '../../feature/publications/view/publications_screen.dart';
import '../../feature/splash/view/splash_screen.dart';
import '../../feature/videos/bloc/videos_cubit.dart';
import '../../feature/videos/models/videos_model.dart';
import '../../feature/videos/view/videos_screen.dart';
import '../../feature/videos/view/youtube_video_player.dart';
import '../../shared/sdm_web_view.dart';

part 'app_router.gr.dart';

/// Auto Route configuration for type-safe navigation
@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
        // Splash Route (initial)
        AutoRoute(
          page: SplashRoute.page,
          path: '/',
          initial: true,
        ),

        // Authentication Routes
        AutoRoute(
          page: PhoneRegistrationRoute.page,
          path: '/phone-registration',
        ),
        AutoRoute(
          page: PhoneOtpRoute.page,
          path: '/phone-otp',
        ),

        // Main App Routes
        AutoRoute(
          page: HomeRoute.page,
          path: '/home',
        ),
        AutoRoute(
          page: ProfileRoute.page,
          path: '/profile',
        ),

        // Content Routes
        AutoRoute(
          page: AboutUsRoute.page,
          path: '/about-us',
        ),
        AutoRoute(
          page: BlogsRoute.page,
          path: '/blogs',
        ),
        AutoRoute(
          page: BlogViewRoute.page,
          path: '/blog-view',
        ),
        AutoRoute(
          page: CalendarEventsRoute.page,
          path: '/calendar-events',
        ),
        AutoRoute(
          page: ContactUsRoute.page,
          path: '/contact-us',
        ),
        AutoRoute(
          page: PublicationsRoute.page,
          path: '/publications',
        ),
        AutoRoute(
          page: PublicationInfoRoute.page,
          path: '/publication-info',
        ),
        AutoRoute(
          page: VideosRoute.page,
          path: '/videos',
        ),

        // Music Routes
        AutoRoute(
          page: MusicListRoute.page,
          path: '/music-list',
        ),
        AutoRoute(
          page: MusicPlayerRoute.page,
          path: '/music-player',
        ),

        // Certificate Routes
        AutoRoute(
          page: EnterEmailRoute.page,
          path: '/enter-email',
        ),
        AutoRoute(
          page: CertificatesRoute.page,
          path: '/certificates',
        ),
        AutoRoute(
          page: CertificateDetailsRoute.page,
          path: '/certificate-details',
        ),

        // Video Player Route
        AutoRoute(
          page: YoutubeVideoPlayerRoute.page,
          path: '/video-player',
        ),

        // Admin Routes
        AutoRoute(
          page: AdminHomeRoute.page,
          path: '/admin-home',
        ),
        AutoRoute(
          page: BlogInputRoute.page,
          path: '/blog-input',
        ),
        AutoRoute(
          page: PublicationInputRoute.page,
          path: '/publication-input',
        ),
        AutoRoute(
          page: QuotesInputRoute.page,
          path: '/quotes-input',
        ),
        AutoRoute(
          page: MusicInputRoute.page,
          path: '/music-input',
        ),

        // Web View Route
        AutoRoute(
          page: SdmWebViewRoute.page,
          path: '/sdm-web-view',
        ),

        // Music Playlist Route
        AutoRoute(
          page: MusicPlaylistRoute.page,
          path: '/music-playlist',
        ),
      ];
}

/// Phone Registration Page
@RoutePage()
class PhoneRegistrationPage extends StatelessWidget {
  const PhoneRegistrationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const PhoneRegistrationScreen();
  }
}

/// Phone OTP Page
@RoutePage()
class PhoneOtpPage extends StatelessWidget {
  const PhoneOtpPage({
    super.key,
    this.verificationId,
  });

  final String? verificationId;

  @override
  Widget build(BuildContext context) {
    return PhoneOtpScreen(verificationId: verificationId);
  }
}

/// Splash Page
@RoutePage()
class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SplashScreen();
  }
}

/// Home Page
@RoutePage()
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const SdmHome();
  }
}

/// Profile Page
@RoutePage()
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileCubit(),
      child: const ProfileScreen(),
    );
  }
}

/// About Us Page
@RoutePage()
class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const AboutUsScreen();
  }
}

/// Blogs Page
@RoutePage()
class BlogsPage extends StatelessWidget {
  const BlogsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BlogsCubit()..fetchBlogs(),
      child: const BlogsScreen(),
    );
  }
}

/// Blog View Page
@RoutePage()
class BlogViewPage extends StatelessWidget {
  const BlogViewPage({super.key, required this.blogModel});

  final BlogModel blogModel;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BlogsCubit(),
      child: BlogView(arguments: blogModel),
    );
  }
}

/// Calendar Events Page
@RoutePage()
class CalendarEventsPage extends StatelessWidget {
  const CalendarEventsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const CalendarEventsScreen();
  }
}

/// Contact Us Page
@RoutePage()
class ContactUsPage extends StatelessWidget {
  const ContactUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const ContactUsScreen();
  }
}

/// Publications Page
@RoutePage()
class PublicationsPage extends StatelessWidget {
  const PublicationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PublicationsCubit()..fetchPublications(),
      child: const PublicationsScreen(),
    );
  }
}

/// Publication Info Page
@RoutePage()
class PublicationInfoPage extends StatelessWidget {
  const PublicationInfoPage({super.key, required this.publicationModel});

  final PublicationModel publicationModel;

  @override
  Widget build(BuildContext context) {
    return PublicationInfo(publicationModel: publicationModel);
  }
}

/// Videos Page
@RoutePage()
class VideosPage extends StatelessWidget {
  const VideosPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => VideosCubit()..fetchVideos(),
      child: const VideosScreen(),
    );
  }
}

/// Music List Page
@RoutePage()
class MusicListPage extends StatelessWidget {
  const MusicListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MusicListCubit(
        musicRepository: context.read<MusicRepository>(),
        profileRepository: context.read<ProfileRepository>(),
      )
        ..fetchMusic()
        ..fetchFavoritesFromUser(),
      child: const MusicList(),
    );
  }
}

/// Music Player Page
@RoutePage()
class MusicPlayerPage extends StatelessWidget {
  const MusicPlayerPage({
    super.key,
    this.musicPlayerArguments,
  });

  final MusicPlayerArguments? musicPlayerArguments;

  @override
  Widget build(BuildContext context) {
    return MusicPlayer(musicPlayerArgs: musicPlayerArguments);
  }
}

/// Enter Email Page
@RoutePage()
class EnterEmailPage extends StatelessWidget {
  const EnterEmailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const EnterEmailScreen();
  }
}

/// Certificates Page
@RoutePage()
class CertificatesPage extends StatelessWidget {
  const CertificatesPage({super.key, this.certificates});

  final AccredibleCredentialsCertificates? certificates;

  @override
  Widget build(BuildContext context) {
    return CertificatesScreen(certificates: certificates);
  }
}

/// Certificate Details Page
@RoutePage()
class CertificateDetailsPage extends StatelessWidget {
  const CertificateDetailsPage({super.key, required this.credential});

  final Credential credential;

  @override
  Widget build(BuildContext context) {
    return CertificateDetails(args: credential);
  }
}

/// Youtube Video Player Page
@RoutePage()
class YoutubeVideoPlayerPage extends StatelessWidget {
  const YoutubeVideoPlayerPage({super.key, required this.videoModel});

  final VideoModel videoModel;

  @override
  Widget build(BuildContext context) {
    return SdmYoutubeVideoPlayer(videoModel: videoModel);
  }
}

/// Admin Home Page
@RoutePage()
class AdminHomePage extends StatelessWidget {
  const AdminHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return const AdminHome();
  }
}

/// Blog Input Page
@RoutePage()
class BlogInputPage extends StatelessWidget {
  const BlogInputPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const BlogsInput();
  }
}

/// Publication Input Page
@RoutePage()
class PublicationInputPage extends StatelessWidget {
  const PublicationInputPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const PublicationsInput();
  }
}

/// Quotes Input Page
@RoutePage()
class QuotesInputPage extends StatelessWidget {
  const QuotesInputPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(),
      child: const QuotesInput(),
    );
  }
}

/// Music Input Page
@RoutePage()
class MusicInputPage extends StatelessWidget {
  const MusicInputPage({super.key, this.musicModel});

  final MusicModel? musicModel;

  @override
  Widget build(BuildContext context) {
    return MusicInputScreen(args: musicModel);
  }
}

/// Web View Page
@RoutePage()
class SdmWebViewPage extends StatelessWidget {
  const SdmWebViewPage({super.key, required this.args});

  final SdmWebViewArguments args;

  @override
  Widget build(BuildContext context) {
    return SdmWebView(args: args);
  }
}

/// Music Playlist Page
@RoutePage()
class MusicPlaylistPage extends StatelessWidget {
  const MusicPlaylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MusicListCubit(
        musicRepository: context.read<MusicRepository>(),
        profileRepository: context.read<ProfileRepository>(),
      )..fetchFavoritesMusic(),
      child: const MusicFavoritesScreen(),
    );
  }
}
