import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../../../core/assets/assets.gen.dart';
import '../../../../core/di/injection.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/theme/sdm_palette.dart';
import '../../../../shared/shared.dart';
import '../../../analytics/cubit/analytics_cubit.dart';
import '../../../analytics/models/sdm_events.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

/// Phone OTP verification screen using clean architecture with Flow Builder support
class PhoneOtpScreen extends StatefulWidget {
  const PhoneOtpScreen({
    super.key,
    this.verificationId,
    this.onNavigateToHome,
  });

  final String? verificationId;

  /// Optional callback for custom navigation (used by Flow Builder)
  final VoidCallback? onNavigateToHome;

  @override
  State<PhoneOtpScreen> createState() => _PhoneOtpScreenState();
}

class _PhoneOtpScreenState extends State<PhoneOtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  String? _verificationId;

  @override
  void initState() {
    super.initState();
    // Use verification ID from widget parameter or route arguments
    _verificationId = widget.verificationId;
    if (_verificationId == null) {
      // Fallback to route arguments for backward compatibility
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args is String) {
          setState(() {
            _verificationId = args;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<AuthBloc>(),
      child: BlocListener<AuthBloc, AuthState>(
        listenWhen: (previous, current) =>
            previous.status != current.status || previous.user != current.user,
        listener: (context, state) async {
          if (state.status == AuthStatus.authenticated && state.user != null) {
            debugPrint('OTP Screen: User authenticated, navigating to home');

            // Configure purchases if available
            if (await Purchases.isConfigured) {
              await Purchases.logIn(state.user!.uid);
            }

            // Small delay for UserSessionListener to process
            await Future.delayed(const Duration(milliseconds: 500));

            if (context.mounted) {
              // Support both Flow Builder callback and traditional navigation
              if (widget.onNavigateToHome != null) {
                // Flow Builder will handle navigation through state changes
                widget.onNavigateToHome!();
              } else {
                // Use auto_route navigation
                context.router.replaceAll([const HomeRoute()]);
              }
            }
          } else if (state.hasError) {
            SdmToast.show(state.errorMessage ?? 'Something went wrong');
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('Verify'),
                foregroundColor: SdmPalette.black,
              ),
              body: ListView(
                physics: const ClampingScrollPhysics(),
                children: [
                  SizedBox(
                    height: 100.h,
                  ),
                  Assets.sdmImages.otpIllustration.image(
                    height: 180.h,
                  ),
                  SizedBox(
                    height: 12.h,
                  ),
                  Text(
                    'Verification',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 17.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 26.0).w,
                    child: Text(
                      'Enter your OTP code number',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: SdmPalette.textColorGrey,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40.0).w,
                    child: Pinput(
                      length: 6,
                      controller: _otpController,
                      keyboardType: TextInputType.number,
                      defaultPinTheme: PinTheme(
                        height: 44.h,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10.r),
                          boxShadow: const [
                            BoxShadow(
                              color: SdmPalette.black29,
                              blurRadius: 4,
                              offset: Offset(0, 4),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 30.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          minWidth: 130.w,
                          maxWidth: 130.w,
                        ),
                        child: SdmPrimaryCta(
                          child: state.isLoading
                              ? const CircularProgressIndicator.adaptive()
                              : Text(
                                  'Verify',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: SdmPalette.white,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                          onPressed: () =>
                              _verifyOtp(context, _otpController.text),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 22.h,
                  ),
                  Column(
                    children: [
                      Text(
                        "Didn't receive any code?",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w400,
                          color: SdmPalette.textColorGrey,
                        ),
                      ),
                      TextButton(
                        onPressed: state.canResendOtp
                            ? () => _resendOtp(context)
                            : null,
                        child: Text(
                          'Resend New Code',
                          style: TextStyle(
                            color: state.canResendOtp
                                ? SdmPalette.lightRed
                                : SdmPalette.textColorGrey,
                            fontWeight: FontWeight.w600,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _verifyOtp(BuildContext context, String otp) {
    if (otp.trim().isEmpty || otp.length < 6) {
      SdmToast.show('Please enter the complete OTP');
      return;
    }

    if (_verificationId == null) {
      SdmToast.show('Verification ID not found. Please try again.');
      Navigator.pop(context);
      return;
    }

    // Track analytics
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(VerifyOtpClickEvent());

    // Verify OTP using clean architecture BLoC
    context.read<AuthBloc>().add(
          AuthEvent.verifyOtp(
            verificationId: _verificationId!,
            smsCode: otp.trim(),
          ),
        );
  }

  void _resendOtp(BuildContext context) {
    // Track analytics
    context.read<AnalyticsCubit>().onTrackAnalyticsEvent(ResendOtpClickEvent());

    // Get current state to access forceResendingToken and phone number
    final authState = context.read<AuthBloc>().state;

    // Check if we have the required information for resend
    if (authState.phoneNumber == null || authState.countryCode == null) {
      // Fallback: navigate back to phone registration
      Navigator.pop(context);
      SdmToast.show('Please enter your phone number again to resend OTP');
      return;
    }

    // Check if resend is allowed (30-second cooldown)
    if (!authState.canResendOtp) {
      SdmToast.show('Please wait before requesting another OTP');
      return;
    }

    // Resend OTP using the stored phone number and force resending token
    context.read<AuthBloc>().add(
          AuthEvent.sendOtp(
            phoneNumber: authState.phoneNumber!,
            countryCode: authState.countryCode!,
            forceResendingToken: authState.forceResendingToken,
          ),
        );

    SdmToast.show('OTP resent successfully');
  }
}
