import 'package:equatable/equatable.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/navigation/navigation_helper.dart';
import 'package:shridattmandir/src/feature/feature.dart';
import 'package:shridattmandir/src/shared/shared.dart';

part 'deeplink_state.dart';

/// Simple, reactive deep link cubit following clean architecture
@injectable
class DeeplinkCubit extends Cubit<DeeplinkState> {
  DeeplinkCubit({
    required SubscriptionService subscriptionService,
    required FirebaseMessaging firebaseMessaging,
    required MusicRepository musicRepository,
    required BlogsRepository blogsRepository,
  })  : _subscriptionService = subscriptionService,
        _firebaseMessaging = firebaseMessaging,
        _musicRepository = musicRepository,
        _blogsRepository = blogsRepository,
        super(const DeeplinkState());

  final SubscriptionService _subscriptionService;
  final FirebaseMessaging _firebaseMessaging;
  final MusicRepository _musicRepository;
  final BlogsRepository _blogsRepository;

  /// Initialize push notifications - clean and simple
  Future<void> initialize() async {
    await _requestPermission();
    await _setupBadge();
    _setupListeners();
    await _handleInitialMessage();
  }

  /// Request notification permissions
  Future<void> _requestPermission() async {
    await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  /// Setup app badge
  Future<void> _setupBadge() async {
    if (await FlutterAppBadger.isAppBadgeSupported()) {
      await FlutterAppBadger.removeBadge();
    }
  }

  /// Setup message listeners
  void _setupListeners() {
    // Foreground messages
    FirebaseMessaging.onMessage.listen((message) {
      safeEmit(state.copyWith(
        messageOnForeground: true,
        remoteMessage: () => message,
      ));
    });

    // Background/terminated app messages
    FirebaseMessaging.onMessageOpenedApp.listen((message) async {
      safeEmit(state.copyWith(
        messageOnForeground: false,
        remoteMessage: () => message,
      ));
      await handleNotification(message);
    });
  }

  /// Handle initial message (cold start)
  Future<void> _handleInitialMessage() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      safeEmit(state.copyWith(
        messageOnForeground: false,
        remoteMessage: () => initialMessage,
      ));
      await handleNotification(initialMessage);
    }
  }

  /// Handle notification - simple and reactive
  Future<void> handleNotification(RemoteMessage message) async {
    safeEmit(state.copyWith(
      messageOnForeground: false,
      remoteMessage: () => message,
    ));

    try {
      await _processNotificationData(message.data);
    } catch (e) {
      debugPrint('Error handling notification: $e');
      SdmToast.show('Failed to navigate from notification');
    }
  }

  /// Process notification data - clean and simple
  Future<void> _processNotificationData(Map<String, dynamic> data) async {
    final route = data['routeName'] as String?;
    final argumentsData = data['arguments'];

    if (route == null) return;

    // Fetch data if needed, then navigate
    final processedData = await _enrichNotificationData(route, argumentsData);
    final updatedData = {
      ...data,
      if (processedData != null) 'arguments': processedData
    };

    await NavigationHelper.navigateFromNotification(
        updatedData, _subscriptionService);
  }

  /// Enrich notification data with fetched content
  Future<Object?> _enrichNotificationData(
      String route, dynamic argumentsData) async {
    return switch (route) {
      '/music-player' when argumentsData is String =>
        await _fetchMusicData(argumentsData),
      '/blog-view' when argumentsData is String =>
        await _fetchBlogData(argumentsData),
      _ => argumentsData,
    };
  }

  /// Fetch music data
  Future<MusicPlayerArguments?> _fetchMusicData(String musicId) async {
    try {
      final music = await _musicRepository.fetchMusicById(id: musicId);
      return music != null ? MusicPlayerArguments(musicList: [music]) : null;
    } catch (e) {
      debugPrint('Error fetching music: $e');
      return null;
    }
  }

  /// Fetch blog data
  Future<BlogModel?> _fetchBlogData(String blogId) async {
    try {
      return await _blogsRepository.fetchBlogById(id: blogId);
    } catch (e) {
      debugPrint('Error fetching blog: $e');
      return null;
    }
  }

  /// Reset state
  void reset() {
    safeEmit(state.copyWith(
      routeName: () => null,
      arguments: () => null,
    ));
  }
}
