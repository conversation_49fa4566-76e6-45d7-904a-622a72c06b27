import 'package:equatable/equatable.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show BlogsRepository, MusicRepository;
import 'package:shridattmandir/src/shared/shared.dart';

part 'deeplink_state.dart';

class DeeplinkCubit extends Cubit<DeeplinkState> {
  DeeplinkCubit({
    required INavigationService navigationService,
    required FirebaseMessaging firebaseMessaging,
    required MusicRepository musicRepository,
    required BlogsRepository blogsRepository,
  })  : _navigationService = navigationService,
        _firebaseMessaging = firebaseMessaging,
        _musicRepository = musicRepository,
        _blogsRepository = blogsRepository,
        super(const DeeplinkState());

  final INavigationService _navigationService;
  final FirebaseMessaging _firebaseMessaging;
  final MusicRepository _musicRepository;
  final BlogsRepository _blogsRepository;

  final String routeName = 'route';
  final String arguments = 'arguments';

  Future<void> initialize() async {
    await _requestPermission();

    if (await FlutterAppBadger.isAppBadgeSupported()) {
      await FlutterAppBadger.removeBadge();
    }

    FirebaseMessaging.onMessage.listen(
      (message) {
        safeEmit(
          state.copyWith(
            messageOnForeground: true,
            remoteMessage: () => message,
          ),
        );
      },
    );

    FirebaseMessaging.onMessageOpenedApp.listen(
      (message) async {
        safeEmit(
          state.copyWith(
            messageOnForeground: false,
            remoteMessage: () => message,
          ),
        );
        await handleNotification(message);
      },
    );

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      safeEmit(
        state.copyWith(
          messageOnForeground: false,
          remoteMessage: () => initialMessage,
        ),
      );
      await handleNotification(initialMessage);
    }
  }

  Future<void> _requestPermission() async {
    final permission = await _firebaseMessaging.requestPermission(
      sound: true,
      badge: true,
      alert: true,
    );

    if (permission.alert == AppleNotificationSetting.enabled) {
      _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
    debugPrint(await _firebaseMessaging.getToken());
  }

  Future<void> handleNotification(RemoteMessage message) async {
    safeEmit(
      state.copyWith(
        messageOnForeground: false,
        remoteMessage: () => message,
      ),
    );

    try {
      // Use navigation service to handle the notification
      final result = await _navigationService.navigateFromPushNotification(
        message.data,
      );

      // Handle special cases that need additional processing
      await _handleSpecialNavigationCases(message.data, result);

      // Update state based on result
      _updateStateFromNavigationResult(result);
    } catch (e) {
      debugPrint('Error handling notification: $e');
      SdmToast.show('Failed to navigate from notification');
    }
  }

  /// Handle special navigation cases that need additional data fetching
  Future<void> _handleSpecialNavigationCases(
    Map<String, dynamic> data,
    NavigationResult result,
  ) async {
    final route = data[routeName] as String?;
    final argumentsData = data[arguments];

    if (route == null) return;

    // Handle cases that need data fetching before navigation
    switch (route) {
      case '/music-player':
        if (argumentsData != null) {
          await _handleMusicPlayerNavigation(argumentsData, result);
        }
        break;

      case '/blog-view':
        if (argumentsData != null) {
          await _handleBlogViewNavigation(argumentsData, result);
        }
        break;

      default:
        // No special handling needed
        break;
    }
  }

  /// Handle music player navigation with data fetching
  Future<void> _handleMusicPlayerNavigation(
    String musicId,
    NavigationResult initialResult,
  ) async {
    // Only proceed if initial navigation was successful or requires action
    if (initialResult is NavigationFailure) return;

    try {
      final musicById = await _musicRepository.fetchMusicById(id: musicId);

      if (musicById != null) {
        // Navigate with the fetched music data
        await _navigationService.navigateToRoute(
          '/music-player',
          arguments: MusicPlayerArguments(musicList: [musicById]),
          source: NavigationSource.pushNotification,
        );
      } else {
        // Fallback to music list if specific music not found
        await _navigationService.navigateToRoute(
          '/music-list',
          source: NavigationSource.pushNotification,
        );
      }
    } catch (e) {
      debugPrint('Error fetching music: $e');
      SdmToast.show('Failed to load music');
    }
  }

  /// Handle blog view navigation with data fetching
  Future<void> _handleBlogViewNavigation(
    String blogId,
    NavigationResult initialResult,
  ) async {
    // Only proceed if initial navigation was successful or requires action
    if (initialResult is NavigationFailure) return;

    try {
      final blogById = await _blogsRepository.fetchBlogById(id: blogId);

      if (blogById != null) {
        // Navigate with the fetched blog data
        await _navigationService.navigateToRoute(
          '/blog-view',
          arguments: blogById,
          source: NavigationSource.pushNotification,
        );
      }
    } catch (e) {
      debugPrint('Error fetching blog: $e');
      SdmToast.show('Failed to load blog');
    }
  }

  /// Update cubit state based on navigation result
  void _updateStateFromNavigationResult(NavigationResult result) {
    switch (result) {
      case NavigationSuccess success:
        safeEmit(
          state.copyWith(
            routeName: () => success.data?['route'] as String?,
            arguments: () => null,
          ),
        );
        break;

      case NavigationRequiresAction requiresAction:
        // Handle paywall or auth requirements
        if (requiresAction.action == NavigationAction.showPaywall) {
          safeEmit(
            state.copyWith(
              routeName: () => '/paywall',
              arguments: () => null,
            ),
          );
        }
        break;

      case NavigationFailure _:
      case NavigationCancelled _:
        // Don't update state for failures
        break;
    }
  }

  void reset() {
    safeEmit(
      state.copyWith(
        routeName: () => null,
        arguments: () => null,
      ),
    );
  }
}
