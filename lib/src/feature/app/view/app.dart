import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:just_audio/just_audio.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:shridattmandir/src/core/core.dart';
import 'package:shridattmandir/src/core/di/injection.dart';
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart';

class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  final _appRouter = AppRouter();

  @override
  Widget build(BuildContext context) {
    return OverlaySupport.global(
      child: MultiRepositoryProvider(
        providers: [
          RepositoryProvider(
            create: (context) => AnalyticsRepository(
              FirebaseAnalytics.instance,
            ),
          ),
          RepositoryProvider(
            create: (context) => MusicRepository(
              FirebaseFirestore.instance,
              FirebaseStorage.instance,
            ),
          ),
          RepositoryProvider(
            create: (context) => PurchasesRepository(),
          ),
          RepositoryProvider(
            create: (context) => BlogsRepository(
              FirebaseFirestore.instance,
            ),
          ),
          RepositoryProvider(
            create: (context) => AccredibleRepository(),
          ),
          RepositoryProvider(
            create: (context) => UserRepository(
              FirebaseFirestore.instance,
            ),
          ),
        ],
        child: MultiBlocProvider(
          providers: [
              BlocProvider(
                create: (context) => PurchasesCubit(
                  firebaseAuth: FirebaseAuth.instance,
                  purchasesRepository: context.read<PurchasesRepository>(),
                  firebaseMessaging: FirebaseMessaging.instance,
                  deviceInfoPlugin: DeviceInfoPlugin(),
                )..initPlatformState(),
                lazy: false,
              ),
              RepositoryProvider(
                create: (context) => SubscriptionService(
                  purchasesCubit: context.read<PurchasesCubit>(),
                ),
              ),
              RepositoryProvider(
                create: (context) => ProfileRepository(
                  FirebaseFirestore.instance,
                  subscriptionService: context.read<SubscriptionService>(),
                ),
              ),
              BlocProvider(
                create: (context) => DeeplinkCubit(
                  subscriptionService: getIt<SubscriptionService>(),
                  firebaseMessaging: FirebaseMessaging.instance,
                  musicRepository: context.read<MusicRepository>(),
                  blogsRepository: context.read<BlogsRepository>(),
                ),
              ),
              BlocProvider(
                create: (context) => getIt<AuthBloc>(),
              ),
              BlocProvider(
                create: (context) => MusicPlayerCubit(
                  audioPlayer: AudioPlayer(),
                  musicRepository: context.read<MusicRepository>(),
                )..musicPlayerListeners(),
                lazy: false,
              ),
              BlocProvider(
                create: (context) => RemoteConfigCubit(),
                lazy: false,
              ),
              BlocProvider(
                create: (context) => ProfileCubit(),
              ),
              BlocProvider(
                create: (context) => UserSessionCubit(),
              ),
              BlocProvider(
                create: (context) => AnalyticsCubit(
                  analyticsRepository: context.read<AnalyticsRepository>(),
                ),
                lazy: false,
              ),
            ],
            child: ScreenUtilInit(
              designSize: const Size(375, 812),
              builder: (context, child) {
                return UserSessionListener(
                  child: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    theme: SdmPalette.sdmThemeData(),
                    routerConfig: _appRouter.config(),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
