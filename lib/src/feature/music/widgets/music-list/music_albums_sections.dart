import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shridattmandir/src/core/core.dart' show SdmPalette, SdmUrls;
import 'package:shridattmandir/src/core/router/app_router.dart';
import 'package:shridattmandir/src/feature/feature.dart'
    show MusicListCubit, MusicListState;
import 'package:shridattmandir/src/shared/sdm_network_image.dart';

class AlbumsSection extends StatelessWidget {
  const AlbumsSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MusicListCubit, MusicListState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.only(left: 16).w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 16.h,
              ),
              Text(
                'Albums',
                style: TextStyle(
                  fontSize: 15.sp,
                  color: SdmPalette.textColorGrey2,
                ),
              ),
              SizedBox(
                height: 12.h,
              ),
              GestureDetector(
                onTap: () async {
                  final musicListCubit = context.read<MusicListCubit>();
                  await musicListCubit.setFavoritesToUser();
                  if (!context.mounted) {
                    return;
                  }
                  context.router.push(const MusicPlaylistRoute());
                },
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(
                          color: SdmPalette.white,
                          width: 2.w,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10.r),
                        child: SdmNetworkImage(
                          url: SdmUrls.favouritesImage,
                          width: 100.w,
                          placeHolderWidget: const Icon(
                            Icons.favorite_border_outlined,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 3.h,
                    ),
                    Text(
                      'Favourites',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: SdmPalette.black,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 24.h,
              ),
              Text(
                state.indexEnum != null ? 'Filter results' : 'All Music',
                style: TextStyle(
                  fontSize: 15.sp,
                  color: SdmPalette.textColorGrey2,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
